defmodule Drops.Operations.NoEctoTest do
  use ExUnit.Case, async: true

  describe "Operations without Ecto" do
    defmodule NoEctoOperations do
      use Drops.Operations
    end

    defmodule SimpleCommand do
      use NoEctoOperations, type: :command

      schema do
        %{
          required(:name) => string(),
          optional(:age) => integer()
        }
      end

      def execute(params) do
        {:ok, "Hello, #{params.name}!"}
      end
    end

    defmodule SimpleQuery do
      use NoEctoOperations, type: :query

      schema do
        %{
          required(:id) => integer()
        }
      end

      def execute(params) do
        {:ok, %{id: params.id, name: "User #{params.id}"}}
      end
    end

    test "command operations work without Ecto" do
      params = %{name: "<PERSON>", age: 30}
      
      assert {:ok, result} = SimpleCommand.call(params)
      assert result.result == "Hello, Alice!"
      assert result.params == %{name: "<PERSON>", age: 30}
      assert result.operation == SimpleCommand
    end

    test "query operations work without <PERSON>cto" do
      params = %{id: 123}
      
      assert {:ok, result} = SimpleQuery.call(params)
      assert result.result == %{id: 123, name: "User 123"}
      assert result.params == %{id: 123}
      assert result.operation == SimpleQuery
    end

    test "validation works without Ecto" do
      # Missing required field
      params = %{age: 30}
      
      assert {:error, failure} = SimpleCommand.call(params)
      assert failure.operation == SimpleCommand
      assert failure.params == params
      
      # Should have validation errors
      assert is_list(failure.result)
      assert length(failure.result) > 0
    end

    test "operations don't have Ecto-specific functions when no repo configured" do
      # These functions should not be available
      refute function_exported?(SimpleCommand, :changeset, 1)
      refute function_exported?(SimpleCommand, :cast_changeset, 2)
      refute function_exported?(SimpleCommand, :persist, 1)
      
      # But these should be available
      assert function_exported?(SimpleCommand, :call, 1)
      assert function_exported?(SimpleCommand, :execute, 1)
      assert function_exported?(SimpleCommand, :prepare, 1)
    end

    test "schema validation works correctly without Ecto" do
      # Valid params
      valid_params = %{name: "Bob", age: 25}
      assert {:ok, _result} = SimpleCommand.call(valid_params)
      
      # Invalid type
      invalid_params = %{name: "Bob", age: "not_an_integer"}
      assert {:error, failure} = SimpleCommand.call(invalid_params)
      assert is_list(failure.result)
    end

    test "prepare function works without Ecto" do
      defmodule CommandWithPrepare do
        use NoEctoOperations, type: :command

        schema do
          %{
            required(:name) => string()
          }
        end

        def prepare(params) do
          Map.put(params, :prepared, true)
        end

        def execute(params) do
          {:ok, params}
        end
      end

      params = %{name: "Alice"}
      assert {:ok, result} = CommandWithPrepare.call(params)
      assert result.params.prepared == true
    end
  end

  describe "Form operations without Ecto" do
    defmodule NoEctoFormOperations do
      use Drops.Operations
    end

    defmodule SimpleForm do
      use NoEctoFormOperations, type: :form

      schema do
        %{
          required(:email) => string(),
          optional(:name) => string()
        }
      end

      def execute(params) do
        {:ok, "Form submitted with #{params.email}"}
      end
    end

    test "form operations work without Ecto" do
      # Test with string keys (typical form input)
      params = %{"email" => "<EMAIL>", "name" => "Test User"}
      
      assert {:ok, result} = SimpleForm.call(params)
      assert result.result == "Form <NAME_EMAIL>"
      assert result.type == :form
      
      # Params should be converted to atom keys due to atomize: true
      assert result.params.email == "<EMAIL>"
      assert result.params.name == "Test User"
    end

    test "form validation works without Ecto" do
      # Missing required field
      params = %{"name" => "Test User"}
      
      assert {:error, failure} = SimpleForm.call(params)
      assert failure.type == :form
      assert is_list(failure.result)
    end
  end
end
